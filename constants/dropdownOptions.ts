// Dropdown options constants for various filters

export const RENT_SALE_OPTIONS = [
  { id: 'rent', label: 'Rent' },
  { id: 'sale', label: 'Sale' },
  { id: 'rent_sale', label: 'Both' },
];

export const MIN_AREA_OPTIONS = [
  { id: '', label: 'Any' },
  { id: '500', label: '500+ sqft' },
  { id: '750', label: '750+ sqft' },
  { id: '1000', label: '1000+ sqft' },
  { id: '1250', label: '1250+ sqft' },
  { id: '1500', label: '1500+ sqft' },
  { id: '2000', label: '2000+ sqft' },
  { id: '2500', label: '2500+ sqft' },
  { id: '3000', label: '3000+ sqft' },
];

export const MAX_AREA_OPTIONS = [
  { id: '', label: 'Any' },
  { id: '750', label: 'Up to 750 sqft' },
  { id: '1000', label: 'Up to 1000 sqft' },
  { id: '1250', label: 'Up to 1250 sqft' },
  { id: '1500', label: 'Up to 1500 sqft' },
  { id: '2000', label: 'Up to 2000 sqft' },
  { id: '2500', label: 'Up to 2500 sqft' },
  { id: '3000', label: 'Up to 3000 sqft' },
  { id: '5000', label: 'Up to 5000 sqft' },
];

export const PRICE_MIN_OPTIONS = [
  { id: '', label: 'Any' },
  { id: '50000', label: 'AED 50,000+' },
  { id: '75000', label: 'AED 75,000+' },
  { id: '100000', label: 'AED 100,000+' },
  { id: '150000', label: 'AED 150,000+' },
  { id: '200000', label: 'AED 200,000+' },
  { id: '300000', label: 'AED 300,000+' },
  { id: '500000', label: 'AED 500,000+' },
  { id: '1000000', label: 'AED 1,000,000+' },
];

export const PRICE_MAX_OPTIONS = [
  { id: '', label: 'Any' },
  { id: '75000', label: 'Up to AED 75,000' },
  { id: '100000', label: 'Up to AED 100,000' },
  { id: '150000', label: 'Up to AED 150,000' },
  { id: '200000', label: 'Up to AED 200,000' },
  { id: '300000', label: 'Up to AED 300,000' },
  { id: '500000', label: 'Up to AED 500,000' },
  { id: '1000000', label: 'Up to AED 1,000,000' },
  { id: '2000000', label: 'Up to AED 2,000,000' },
];

// Transform API data to dropdown format
export const transformApiDataToDropdownOptions = (data: any[]) => {
  if (!Array.isArray(data)) return [];
  
  return data.map((item) => ({
    id: item.id?.toString() || '',
    label: item.name || item.title || item.label || 'Unknown',
    value: item,
  }));
};
