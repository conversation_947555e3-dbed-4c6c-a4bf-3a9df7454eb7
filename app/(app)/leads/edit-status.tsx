import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  FlatList,
  Dimensions,
  Modal,
} from 'react-native';
import { useLocalSearchParams, Stack, router } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import DateTimePicker from '@react-native-community/datetimepicker';
import {
  fetchLeadStatuses,
  fetchGeographies,
  fetchBedrooms,
  fetchListings,
  api
} from '@/lib/api';

import ListingCard from '@/components/ListingCard';
import Pagination from '@/components/Pagination';
import ViewingScheduleForm from '@/components/ViewingScheduleForm';
import MeetingConfigForm from '@/components/MeetingConfigForm';
import FollowUpConfigForm from '@/components/FollowUpConfigForm';

import ViewingConfigForm from '@/components/ViewingConfigForm';
import PropertySearchModal from '@/components/PropertySearchModal';
import {
  transformApiDataToDropdownOptions
} from '@/constants/dropdownOptions';

interface StatusOption {
  id: number;
  name: string;
  background_color: string;
  is_disabled: number;
}

interface MeetingConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface FollowUpConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}



interface ViewingConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  minArea: string;
  maxArea: string;
  priceMin: string;
  priceMax: string;
  selectedProperties: string[];
  // Reminder fields
  reminderTitle: string;
  reminderContent: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

const { height: screenHeight } = Dimensions.get('window');
const ITEMS_PER_PAGE = 10;

export default function EditStatusScreen() {
  const { leadId, currentStatus } = useLocalSearchParams();
  const queryClient = useQueryClient();
  const [selectedStatus, setSelectedStatus] = useState(currentStatus?.toString() || '');
  const [showMeetingConfig, setShowMeetingConfig] = useState(false);
  const [showViewingConfig, setShowViewingConfig] = useState(false);
  const [showFollowUpConfig, setShowFollowUpConfig] = useState(false);
  const [currentViewingPage, setCurrentViewingPage] = useState(1);
  const [showViewingModal, setShowViewingModal] = useState(false);
  const [viewingModalStep, setViewingModalStep] = useState<'properties' | 'schedule'>('properties');
  const [showPropertySearchModal, setShowPropertySearchModal] = useState(false);

  // Date picker states
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [currentConfigType, setCurrentConfigType] = useState<'meeting' | 'followUp' | 'viewing'>('viewing');

  // Configuration states
  const [meetingConfig, setMeetingConfig] = useState<MeetingConfig>({
    title: 'Meeting appointment',
    content: 'Meeting appointment reminder.',
    dueDate: '',
    priority: 'Low',
    sendEmail: false,
    remarks: '',
  });

  const [viewingConfig, setViewingConfig] = useState<ViewingConfig>({
    search: '',
    rentSale: '',
    towerBuilding: '',
    bedrooms: [],
    minArea: '',
    maxArea: '',
    priceMin: '',
    priceMax: '',
    selectedProperties: [],
    // Reminder fields
    reminderTitle: 'Visit appointment',
    reminderContent: 'This is just a reminder that the following listings needs to be seen.',
    dueDate: '',
    priority: 'Low',
    sendEmail: false,
    remarks: '',
  });

  // Separate state for selected properties to avoid re-triggering queries
  const [selectedProperties, setSelectedProperties] = useState<string[]>([]);

  const [followUpConfig, setFollowUpConfig] = useState<FollowUpConfig>({
    title: 'Follow up',
    content: 'Follow up reminder.',
    dueDate: '',
    priority: 'Low',
    sendEmail: false,
    remarks: '',
  });



  // Fetch lead statuses
  const { data: leadStatuses = [] } = useQuery({
    queryKey: ['leadStatuses'],
    queryFn: fetchLeadStatuses,
  });

  // Fetch dropdown data for configurations
  const { data: geographies = [] } = useQuery({
    queryKey: ['geographies'],
    queryFn: fetchGeographies,
  });

  const { data: bedrooms = [] } = useQuery({
    queryKey: ['bedrooms'],
    queryFn: fetchBedrooms,
  });

  // Transform API data to dropdown format
  const locationOptions = transformApiDataToDropdownOptions(geographies);
  const bedroomOptions = transformApiDataToDropdownOptions(bedrooms);

  // Get selected location object for towers query
  const selectedLocation = geographies.find((geo: any) => geo.id?.toString() === viewingConfig.search);

  const { data: towers = [{ id: 'any', name: 'Any' }] } = useQuery({
    queryKey: ['towers', selectedLocation?.id],
    queryFn: async () => {
      if (!selectedLocation?.id) {
        return [{ id: 'any', name: 'Any' }];
      }
      try {
        const { data } = await api.get(`/geography/${selectedLocation.id}/towers`);
        return [{ id: 'any', name: 'Any' }, ...data];
      } catch (error) {
        console.error('Error fetching towers:', error);
        return [{ id: 'any', name: 'Any' }];
      }
    },
    enabled: true,
  });

  // Transform towers to dropdown format
  const towerBuildingOptions = transformApiDataToDropdownOptions(towers);
  const finalTowerOptions = towerBuildingOptions.length > 0 ? towerBuildingOptions : [
    { id: 'any', label: 'Any' },
    { id: 'test', label: 'Test Tower' }
  ];

  const enabledStatuses = leadStatuses.filter((status: StatusOption) => status.is_disabled === 0);

  // Create filters object for listings API
  // Note: API only supports propertyType, adType, location, vt parameters
  const createListingFilters = (config: ViewingConfig) => {
    const filters: any = {};

    if (config.search) {
      filters.location = config.search;
    }

    if (config.rentSale) {
      const rentSaleMap: any = {
        'rent': 'rent',
        'sale': 'sale',
        'rent_sale': 'All'
      };
      filters.adType = rentSaleMap[config.rentSale] || 'All';
    }

    // Note: Other filters (tower, bedrooms, areas, prices) are not supported by the API
    // They will be used for UI filtering only

    return filters;
  };

  // Check if any API-supported filters are applied
  const hasFilters = viewingConfig?.search || viewingConfig?.rentSale;

  // Check if any UI-only filters are applied (for display purposes)
  const hasUIFilters = viewingConfig?.towerBuilding ||
    (viewingConfig?.bedrooms && viewingConfig.bedrooms.length > 0) ||
    viewingConfig?.priceMin || viewingConfig?.priceMax;

  // Create config without selectedProperties for query key
  const viewingConfigForQuery = {
    search: viewingConfig.search,
    rentSale: viewingConfig.rentSale,
    towerBuilding: viewingConfig.towerBuilding,
    bedrooms: viewingConfig.bedrooms,
    minArea: viewingConfig.minArea,
    maxArea: viewingConfig.maxArea,
    priceMin: viewingConfig.priceMin,
    priceMax: viewingConfig.priceMax,
  };

  // Fetch listings with pagination - always enabled when viewing config is shown
  const { data: listingsData, isLoading: isLoadingListings } = useQuery({
    queryKey: ['modal-listings', viewingConfigForQuery, currentViewingPage],
    queryFn: () => {
      if (!hasFilters) {
        const params = {
          propertyType: undefined as number | undefined,
          adType: 'All' as string,
          vt: 'master' as string
        };
        return fetchListings(currentViewingPage, params);
      }
      const filters = createListingFilters(viewingConfig);
      const params = {
        ...filters,
        vt: 'master',
        adType: filters.adType || 'All'
      };
      return fetchListings(currentViewingPage, params);
    },
    enabled: showViewingConfig, // Keep this to only fetch when viewing config is active
  });

  // Separate query for getting total count without pagination for the button
  const { data: totalCountData } = useQuery({
    queryKey: ['viewing-total-count', viewingConfigForQuery],
    queryFn: () => {
      if (!hasFilters) {
        const params = {
          propertyType: undefined as number | undefined,
          adType: 'All' as string,
          vt: 'master' as string
        };
        return fetchListings(1, params); // Just get first page to get total count
      }
      const filters = createListingFilters(viewingConfig);
      const params = {
        ...filters,
        vt: 'master',
        adType: filters.adType || 'All'
      };
      return fetchListings(1, params); // Just get first page to get total count
    },
    enabled: showViewingConfig, // Only fetch when viewing config is active
  });

  // Apply client-side filtering for unsupported API filters
  const applyClientSideFilters = (listings: any[]) => {
    if (!hasUIFilters) return listings;

    return listings.filter(listing => {
      // Tower/Building filter - using tower.id from Listing interface
      if (viewingConfig.towerBuilding && viewingConfig.towerBuilding !== 'any') {
        if (listing.tower?.id?.toString() !== viewingConfig.towerBuilding) {
          return false;
        }
      }

      // Bedrooms filter - using bedrooms_no from Listing interface
      if (viewingConfig.bedrooms && viewingConfig.bedrooms.length > 0) {
        if (!viewingConfig.bedrooms.includes(listing.bedrooms_no?.toString())) {
          return false;
        }
      }

      // Price filters - using price field (string) from Listing interface
      if (viewingConfig.priceMin) {
        const minPrice = parseInt(viewingConfig.priceMin);
        const listingPrice = parseInt(listing.price?.replace(/[^0-9]/g, '') || '0');
        if (listingPrice < minPrice) {
          return false;
        }
      }

      if (viewingConfig.priceMax) {
        const maxPrice = parseInt(viewingConfig.priceMax);
        const listingPrice = parseInt(listing.price?.replace(/[^0-9]/g, '') || '0');
        if (listingPrice > maxPrice) {
          return false;
        }
      }

      // Note: Area filters (minArea, maxArea) are not available in Listing interface
      // These would need to be handled differently or removed from UI

      return true;
    });
  };

  const rawListings = listingsData?.data || [];
  const listings = applyClientSideFilters(rawListings);
  const totalListings = listingsData?.total || 0;
  const totalViewingPages = Math.ceil(totalListings / ITEMS_PER_PAGE);

  // Use total count for button display - apply client-side filtering to get accurate count
  const rawButtonListings = totalCountData?.data || [];
  const filteredButtonListings = applyClientSideFilters(rawButtonListings);
  const buttonTotalCount = hasUIFilters ? filteredButtonListings.length : (totalCountData?.total || 0);



  useEffect(() => {
    setSelectedStatus(currentStatus?.toString() || '');
  }, [currentStatus]);

  const formatStatusName = (name: string) => {
    return name.replace(/_/g, ' ');
  };

  const updateMeetingConfig = (field: keyof MeetingConfig, value: string | boolean) => {
    setMeetingConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateViewingConfig = (field: keyof ViewingConfig, value: string | string[] | (string | number)[] | boolean) => {
    setViewingConfig(prev => ({
      ...prev,
      [field]: value
    }));

    // Sync selectedProperties state when selectedProperties field is updated
    if (field === 'selectedProperties' && Array.isArray(value)) {
      setSelectedProperties(value as string[]);
    }
  };

  const updateFollowUpConfig = (field: keyof FollowUpConfig, value: string | boolean) => {
    setFollowUpConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };



  const handleDatePress = (configType: 'meeting' | 'followUp' | 'viewing') => {
    console.log('Date picker pressed for:', configType);
    setCurrentConfigType(configType);
    setShowDatePicker(true);
  };

  const handleDateChange = (_event: any, selectedDate?: Date) => {
    setShowDatePicker(false);

    if (selectedDate) {
      const formattedDate = selectedDate.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }) + ', ' + selectedDate.toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      });

      if (currentConfigType === 'meeting') {
        updateMeetingConfig('dueDate', formattedDate);
      } else if (currentConfigType === 'followUp') {
        updateFollowUpConfig('dueDate', formattedDate);
      } else if (currentConfigType === 'viewing') {
        updateViewingConfig('dueDate', formattedDate);
      }
    }
  };

  const handleStatusToggle = (statusId: string) => {
    setSelectedStatus(statusId);

    // Reset all config states first
    setShowMeetingConfig(false);
    setShowViewingConfig(false);
    setShowFollowUpConfig(false);

    // Reset viewing filters and selected properties when changing status
    resetViewingFilters();
    setSelectedProperties([]);
    setShowViewingModal(false);
    setViewingModalStep('properties');

    // Check if status requires configuration
    const selectedStatusObj = leadStatuses.find((s: StatusOption) => s.id.toString() === statusId);
    if (selectedStatusObj) {
      if (selectedStatusObj.name === 'MEETING_SCHEDULED') {
        setShowMeetingConfig(true);
      } else if (selectedStatusObj.name === 'VIEWING_SCHEDULED') {
        setCurrentViewingPage(1); // Reset to first page when opening modal
        setShowViewingConfig(true);
      } else if (selectedStatusObj.name === 'FOLLOW_UP') {
        setShowFollowUpConfig(true);
      } else {
        // For simple statuses, just select them without saving automatically
        // User can save manually using the save button
      }
    }
  };

  // Mutation for updating lead status
  const updateStatusMutation = useMutation({
    mutationFn: async ({ statusId, config }: { statusId: string, config?: any }) => {
      const updateData: any = {
        status_id: parseInt(statusId),
      };

      // Handle viewing schedule configuration
      if (config && config.selectedProperties && config.selectedProperties.length > 0) {
        // Get the ref_no for each selected property
        const selectedPropertyRefNos = config.selectedProperties.map((propertyId: string) => {
          const property = listings.find(listing => listing.id.toString() === propertyId);
          return property?.ref_no;
        }).filter(Boolean); // Remove any undefined values

        updateData.viewing_schedule = {
          properties: selectedPropertyRefNos,
          filters: {
            search: config.search,
            rentSale: config.rentSale,
            towerBuilding: config.towerBuilding,
            bedrooms: config.bedrooms,
            priceMin: config.priceMin,
            priceMax: config.priceMax,
          }
        };
      }

      // Add other config data if needed for specific statuses
      if (config && !config.selectedProperties) {
        updateData.config = config;
      }

      console.log('Sending update data:', updateData);
      const response = await api.put(`/leads/${leadId}`, updateData);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['lead', leadId] });
      queryClient.invalidateQueries({ queryKey: ['leads'] });

      // Navigate back
      router.back();
    },
    onError: (error) => {
      console.error('Error updating lead status:', error);
      // Handle error (show toast, etc.)
    },
  });

  const handleSaveStatus = (statusId: string, config?: any) => {
    console.log('Saving status:', statusId, 'with config:', config);
    updateStatusMutation.mutate({ statusId, config });
  };

  const handleConfigSave = (config: any) => {
    handleSaveStatus(selectedStatus, config);
  };

  // Handle listing selection
  const toggleListingSelection = useCallback((listingId: string) => {
    setSelectedProperties(prev =>
      prev.includes(listingId)
        ? prev.filter(id => id !== listingId)
        : [...prev, listingId]
    );
  }, []);

  // Render listing item
  const renderListingItem = useCallback(({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.listingItem}
      onPress={() => toggleListingSelection(item.id.toString())}
      activeOpacity={0.7}
    >
      <View style={styles.listingCheckboxContainer}>
        <View
          style={[
            styles.listingCheckbox,
            selectedProperties.includes(item.id.toString()) && styles.listingCheckboxSelected
          ]}
        >
          {selectedProperties.includes(item.id.toString()) && (
            <Text style={styles.listingCheckmark}>✓</Text>
          )}
        </View>
      </View>
      <View style={styles.listingCardContainer}>
        <ListingCard
          listing={item}
          disableNavigation={true}
          compact={true}
        />
      </View>
    </TouchableOpacity>
  ), [selectedProperties, toggleListingSelection]);

  const getItemLayout = useCallback((_: any, index: number) => ({
    length: 90,
    offset: 90 * index,
    index,
  }), []);

  const resetViewingFilters = () => {
    setViewingConfig({
      search: '',
      rentSale: '',
      towerBuilding: '',
      bedrooms: [],
      minArea: '',
      maxArea: '',
      priceMin: '',
      priceMax: '',
      selectedProperties: [],
      // Reminder fields
      reminderTitle: 'Visit appointment',
      reminderContent: 'This is just a reminder that the following listings needs to be seen.',
      dueDate: '',
      priority: 'Low',
      sendEmail: false,
      remarks: '',
    });
    setSelectedProperties([]);
    setCurrentViewingPage(1);
    queryClient.removeQueries({ queryKey: ['modal-listings'] });
  };

  const handleViewingPageChange = (page: number) => {
    setCurrentViewingPage(page);
  };

  const handleOpenViewingModal = () => {
    setCurrentViewingPage(1); // Reset to first page when opening modal
    setShowViewingModal(true);
    setViewingModalStep('properties');
  };

  const handleCloseViewingModal = () => {
    setShowViewingModal(false);
    setViewingModalStep('properties');
    // Reset selected properties when closing modal
    setSelectedProperties([]);
  };

  const handleScheduleViewing = () => {
    setViewingModalStep('schedule');
  };

  const handleScheduleViewingSave = () => {
    const configWithSelectedProperties = {
      ...viewingConfig,
      selectedProperties: selectedProperties
    };
    handleConfigSave(configWithSelectedProperties);
    handleCloseViewingModal();
  };



  const handleBack = () => {
    router.back();
  };

  // Property search modal handlers
  const handleOpenPropertySearchModal = () => {
    setShowPropertySearchModal(true);
  };

  const handleClosePropertySearchModal = () => {
    setShowPropertySearchModal(false);
  };

  const handleSelectPropertiesFromSearch = (selectedPropertyIds: string[]) => {
    setSelectedProperties(selectedPropertyIds);
    updateViewingConfig('selectedProperties', selectedPropertyIds);
    setShowPropertySearchModal(false);
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <ArrowLeft size={24} color="#000" />
          </TouchableOpacity>
          <Text style={styles.title}>Select Status</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Status Pills */}
        <View style={styles.statusContainer}>
          <ScrollView
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.statusPillsWrapper}
          >
            {enabledStatuses.map((status: StatusOption) => (
              <TouchableOpacity
                key={status.id}
                style={[
                  styles.statusPill,
                  selectedStatus === status.id.toString() && styles.selectedStatusPill
                ]}
                onPress={() => handleStatusToggle(status.id.toString())}
              >
                <Text
                  style={[
                    styles.statusPillText,
                    selectedStatus === status.id.toString() && styles.selectedStatusPillText
                  ]}
                >
                  {formatStatusName(status.name)}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Configuration Content */}
        <ScrollView style={styles.content}>


          {/* Meeting Configuration */}
          {showMeetingConfig && (
            <MeetingConfigForm
              meetingConfig={meetingConfig}
              updateMeetingConfig={updateMeetingConfig}
              handleDatePress={handleDatePress}
              onSave={handleConfigSave}
            />
          )}

          {/* Follow Up Configuration */}
          {showFollowUpConfig && (
            <FollowUpConfigForm
              followUpConfig={followUpConfig}
              updateFollowUpConfig={updateFollowUpConfig}
              handleDatePress={handleDatePress}
              onSave={handleConfigSave}
            />
          )}



          {/* Viewing Configuration */}
          {showViewingConfig && (
            <ViewingConfigForm
              viewingConfig={viewingConfig}
              updateViewingConfig={updateViewingConfig}
              selectedProperties={selectedProperties}
              listings={listings}
              onSearchProperties={handleOpenPropertySearchModal}
            />
          )}

          {!showMeetingConfig && !showViewingConfig && !showFollowUpConfig && (
            <View style={styles.instructionContainer}>
              <Text style={styles.instructionText}>
                Select a status above. Some statuses will show additional configuration options.
              </Text>
            </View>
          )}
        </ScrollView>

        {/* Save Button */}
        {selectedStatus && (
          <View style={styles.saveButtonContainer}>
            <TouchableOpacity
              style={[
                styles.saveButton,
                updateStatusMutation.isPending && styles.saveButtonDisabled
              ]}
              onPress={() => handleSaveStatus(selectedStatus)}
              disabled={updateStatusMutation.isPending}
            >
              <Text style={styles.saveButtonText}>
                {updateStatusMutation.isPending ? 'Saving...' : 'Save Status'}
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </SafeAreaView>

      {/* Viewing Modal with 2 Steps */}
      <Modal
        visible={showViewingModal}
        transparent={true}
        animationType="slide"
        onRequestClose={handleCloseViewingModal}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            {/* Header */}
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {viewingModalStep === 'properties' ? 'Select Properties' : 'Schedule Viewing'}
              </Text>
              <TouchableOpacity onPress={handleCloseViewingModal} style={styles.modalCloseButton}>
                <Text style={styles.modalCloseText}>✕</Text>
              </TouchableOpacity>
            </View>

            {/* Step 1: Properties List */}
            {viewingModalStep === 'properties' && (
              <View style={styles.modalContent}>
                {isLoadingListings ? (
                  <View style={styles.loadingContainer}>
                    <Text style={styles.loadingText}>Loading properties...</Text>
                  </View>
                ) : listings.length > 0 ? (
                  <>
                    <Text style={styles.propertiesCount}>
                      {listings.length} of {totalListings} properties
                    </Text>
                    <FlatList
                      data={listings}
                      keyExtractor={(item) => item.id.toString()}
                      renderItem={renderListingItem}
                      getItemLayout={getItemLayout}
                      style={styles.modalListingsList}
                      showsVerticalScrollIndicator={false}
                      removeClippedSubviews={true}
                      maxToRenderPerBatch={10}
                      windowSize={10}
                      ListFooterComponent={() =>
                        totalViewingPages > 1 ? (
                          <View style={styles.modalPaginationContainer}>
                            <Pagination
                              currentPage={currentViewingPage}
                              totalPages={totalViewingPages}
                              onPageChange={handleViewingPageChange}
                            />
                          </View>
                        ) : null
                      }
                    />
                    <View style={styles.modalButtonContainer}>
                      <TouchableOpacity
                        style={[
                          styles.scheduleButton,
                          selectedProperties.length === 0 && styles.scheduleButtonDisabled
                        ]}
                        disabled={selectedProperties.length === 0}
                        onPress={handleScheduleViewing}
                      >
                        <Text style={[
                          styles.scheduleButtonText,
                          selectedProperties.length === 0 && styles.scheduleButtonTextDisabled
                        ]}>
                          Schedule viewing ({selectedProperties.length})
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </>
                ) : (
                  <View style={styles.noPropertiesContainer}>
                    <Text style={styles.noPropertiesText}>No properties found</Text>
                  </View>
                )}
              </View>
            )}

            {/* Step 2: Schedule Form */}
            {viewingModalStep === 'schedule' && (
              <ViewingScheduleForm
                selectedProperties={selectedProperties}
                listings={listings}
                viewingConfig={viewingConfig}
                updateViewingConfig={updateViewingConfig}
                handleDatePress={handleDatePress}
                onBack={() => setViewingModalStep('properties')}
                onConfirm={handleScheduleViewingSave}
              />
            )}
          </View>
        </View>
      </Modal>

      {/* Property Search Modal */}
      <PropertySearchModal
        visible={showPropertySearchModal}
        onClose={handleClosePropertySearchModal}
        onSelectProperties={handleSelectPropertiesFromSearch}
        initialSelectedProperties={selectedProperties}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  placeholder: {
    width: 40,
  },
  statusContainer: {
    padding: 16,
  },
  statusPillsWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  statusPill: {
    paddingHorizontal: 15,
    paddingVertical: 7,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#B89C4C',
    backgroundColor: 'transparent',
  },
  selectedStatusPill: {
    backgroundColor: '#B89C4C',
  },
  statusPillText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#B89C4C',
    textTransform: 'capitalize',
  },
  selectedStatusPillText: {
    color: '#fff',
  },
  content: {
    flex: 1,
  },
  instructionContainer: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  instructionText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  saveButtonContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: '#fff',
  },
  saveButton: {
    backgroundColor: '#B89C4C',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },

  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#111827',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  dateInput: {
    flex: 1,
    paddingRight: 40,
  },
  calendarButton: {
    position: 'absolute',
    right: 8,
    top: 10,
    padding: 4,
  },
  inlineDatePicker: {
    marginTop: 8,
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
  },
  dropdownButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownText: {
    fontSize: 16,
    color: '#111827',
  },
  dropdownArrow: {
    fontSize: 12,
    color: '#6B7280',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  checkboxLabel: {
    fontSize: 16,
    color: '#111827',
    marginLeft: 12,
  },

  // Viewing Configuration Styles
  fullWidthContainer: {
    marginBottom: 16,
  },
  pillScrollContainer: {
    flexDirection: 'row',
  },
  pillScrollContent: {
    paddingRight: 16,
  },
  filterPill: {
    paddingHorizontal: 15,
    paddingVertical: 7,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#B89C4C',
    backgroundColor: 'transparent',
    marginRight: 8,
  },
  selectedFilterPill: {
    backgroundColor: '#B89C4C',
  },
  filterPillText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#B89C4C',
  },
  selectedFilterPillText: {
    color: '#fff',
  },
  viewingButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 16,
    gap: 12,
  },
  resetButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  resetButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  propertiesButton: {
    flex: 2,
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  propertiesButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: screenHeight * 0.9,
    minHeight: screenHeight * 0.7,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  modalCloseButton: {
    padding: 4,
  },
  modalCloseText: {
    fontSize: 18,
    color: '#6B7280',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  propertiesCount: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
  },
  modalListingsList: {
    flex: 1,
  },
  modalPaginationContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
  modalButtonContainer: {
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    flexDirection: 'row',
    gap: 12,
  },
  scheduleButton: {
    flex: 1,
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  scheduleButtonDisabled: {
    backgroundColor: '#E5E7EB',
  },
  scheduleButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
  scheduleButtonTextDisabled: {
    color: '#9CA3AF',
  },
  noPropertiesContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noPropertiesText: {
    fontSize: 16,
    color: '#6B7280',
  },


  dateButton: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginTop: 8,
  },
  dateButtonText: {
    fontSize: 14,
    color: '#6B7280',
  },
  priorityContainer: {
    flexDirection: 'row',
    marginTop: 8,
    gap: 8,
  },
  priorityButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#fff',
  },
  priorityButtonActive: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  priorityButtonText: {
    fontSize: 14,
    color: '#6B7280',
  },
  priorityButtonTextActive: {
    color: '#fff',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  switchLabel: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },

  // Listing Item Styles (for modal)
  listingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 8,
  },
  listingCheckboxContainer: {
    marginRight: 12,
  },
  listingCheckbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  listingCheckboxSelected: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  listingCheckmark: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  listingCardContainer: {
    flex: 1,
  },

  // Config container styles
  configContainer: {
    padding: 16,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    margin: 16,
  },
  configTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
  },
  configDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 16,
    lineHeight: 20,
  },
  openModalButton: {
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  openModalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },

  // Properties list styles
  propertiesListContainer: {
    flex: 1,
    marginTop: 16,
  },
  propertiesListTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },

  // Summary styles
  summaryContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  summaryPropertyItem: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#B89C4C',
  },
  summaryPropertyText: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },

  // Button styles
  secondaryButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginRight: 8,
  },
  secondaryButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  primaryButton: {
    flex: 1,
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  primaryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
});
