import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  FlatList,
} from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { fetchListings } from '@/lib/api';
import ListingCard from '@/components/ListingCard';
import Pagination from '@/components/Pagination';

interface OfferNegotiationConfig {
  selectedProperties: string[];
}

interface OfferNegotiationFormProps {
  selectedProperties: string[];
  onPropertiesChange: (properties: string[]) => void;
  onSave: (config: { selectedProperties: string[] }) => void;
}

const ITEMS_PER_PAGE = 10;

export default function OfferNegotiationForm({
  selectedProperties,
  onPropertiesChange,
  onSave,
}: OfferNegotiationFormProps) {
  const [currentPage, setCurrentPage] = useState(1);

  const [offerConfig, setOfferConfig] = useState<OfferNegotiationConfig>({
    selectedProperties: selectedProperties,
  });

  // Fetch listings with pagination - show all listings without filters
  const { data: listingsData, isLoading: isLoadingListings } = useQuery({
    queryKey: ['offer-listings', currentPage],
    queryFn: () => {
      const params = {
        propertyType: undefined as number | undefined,
        adType: 'All' as string,
        vt: 'master' as string
      };
      return fetchListings(currentPage, params);
    },
    enabled: true,
  });

  const listings = listingsData?.data || [];
  const totalListings = listingsData?.total || 0;
  const totalPages = Math.ceil(totalListings / ITEMS_PER_PAGE);

  // Handle listing selection
  const toggleListingSelection = useCallback((listingId: string) => {
    const newSelectedProperties = offerConfig.selectedProperties.includes(listingId)
      ? offerConfig.selectedProperties.filter(id => id !== listingId)
      : [...offerConfig.selectedProperties, listingId];

    setOfferConfig(prev => ({ ...prev, selectedProperties: newSelectedProperties }));
    onPropertiesChange(newSelectedProperties);
  }, [offerConfig.selectedProperties, onPropertiesChange]);

  const handleRemoveProperty = (propertyId: string) => {
    const updatedProperties = offerConfig.selectedProperties.filter(id => id !== propertyId);
    setOfferConfig(prev => ({ ...prev, selectedProperties: updatedProperties }));
    onPropertiesChange(updatedProperties);
  };

  const handleSave = () => {
    onSave({
      selectedProperties: offerConfig.selectedProperties,
    });
  };

  // Render listing item
  const renderListingItem = useCallback(({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.listingItem}
      onPress={() => toggleListingSelection(item.id.toString())}
      activeOpacity={0.7}
    >
      <View style={styles.listingCheckboxContainer}>
        <View
          style={[
            styles.listingCheckbox,
            offerConfig.selectedProperties.includes(item.id.toString()) && styles.listingCheckboxSelected
          ]}
        >
          {offerConfig.selectedProperties.includes(item.id.toString()) && (
            <Text style={styles.listingCheckmark}>✓</Text>
          )}
        </View>
      </View>
      <View style={styles.listingCardContainer}>
        <ListingCard
          listing={item}
          disableNavigation={true}
          compact={true}
        />
      </View>
    </TouchableOpacity>
  ), [offerConfig.selectedProperties, toggleListingSelection]);

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Select Properties for Offer</Text>

      {/* Selected Properties Count */}
      <Text style={styles.selectedCount}>
        {offerConfig.selectedProperties.length} properties selected
      </Text>

      {/* Properties List */}
      <View style={styles.propertiesContainer}>
        {isLoadingListings ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading properties...</Text>
          </View>
        ) : listings.length > 0 ? (
          <>
            <Text style={styles.propertiesCount}>
              {listings.length} of {totalListings} properties
            </Text>
            <FlatList
              data={listings}
              keyExtractor={(item) => item.id.toString()}
              renderItem={renderListingItem}
              style={styles.propertiesList}
              showsVerticalScrollIndicator={false}
              scrollEnabled={false}
            />

            {/* Pagination */}
            {totalPages > 1 && (
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
            )}
          </>
        ) : (
          <View style={styles.noPropertiesContainer}>
            <Text style={styles.noPropertiesText}>No properties found</Text>
          </View>
        )}
      </View>

      {/* Selected Properties Display */}
      {offerConfig.selectedProperties.length > 0 && (
        <View style={styles.selectedPropertiesContainer}>
          <Text style={styles.selectedPropertiesTitle}>Selected Properties:</Text>
          <ScrollView style={styles.selectedPropertiesList} showsVerticalScrollIndicator={false}>
            {offerConfig.selectedProperties.map((propertyId, index) => {
              const property = listings.find(p => p.id.toString() === propertyId);
              return (
                <View key={propertyId} style={styles.selectedPropertyItem}>
                  <Text style={styles.selectedPropertyText}>
                    {property ? `${property.ref_no} - ${property.title || 'Property'}` : `Property ${index + 1}: ${propertyId}`}
                  </Text>
                  <TouchableOpacity
                    onPress={() => handleRemoveProperty(propertyId)}
                    style={styles.removePropertyButton}
                  >
                    <Text style={styles.removePropertyButtonText}>✕</Text>
                  </TouchableOpacity>
                </View>
              );
            })}
          </ScrollView>
        </View>
      )}

      {/* Save Button */}
      <TouchableOpacity
        style={[
          styles.saveButton,
          offerConfig.selectedProperties.length === 0 && styles.saveButtonDisabled
        ]}
        onPress={handleSave}
        disabled={offerConfig.selectedProperties.length === 0}
      >
        <Text style={[
          styles.saveButtonText,
          offerConfig.selectedProperties.length === 0 && styles.saveButtonTextDisabled
        ]}>
          Save Offer ({offerConfig.selectedProperties.length} properties)
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },

  selectedCount: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
    fontWeight: '500',
  },
  // Properties List styles
  propertiesContainer: {
    marginBottom: 16,
  },
  propertiesCount: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
    fontWeight: '500',
  },
  propertiesList: {
    maxHeight: 400,
  },
  listingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 8,
  },
  listingCheckboxContainer: {
    marginRight: 12,
  },
  listingCheckbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  listingCheckboxSelected: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  listingCheckmark: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  listingCardContainer: {
    flex: 1,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  noPropertiesContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  noPropertiesText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  // Selected Properties styles
  selectedPropertiesContainer: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    maxHeight: 150,
  },
  selectedPropertiesTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  selectedPropertiesList: {
    maxHeight: 100,
  },
  selectedPropertyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 6,
    padding: 8,
    marginBottom: 4,
  },
  selectedPropertyText: {
    fontSize: 12,
    color: '#1F2937',
    flex: 1,
  },
  removePropertyButton: {
    backgroundColor: '#EF4444',
    borderRadius: 4,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  removePropertyButtonText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: 'bold',
  },
  // Save Button styles
  saveButton: {
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginTop: 16,
  },
  saveButtonDisabled: {
    backgroundColor: '#E5E7EB',
  },
  saveButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '600',
  },
  saveButtonTextDisabled: {
    color: '#9CA3AF',
  },
});
