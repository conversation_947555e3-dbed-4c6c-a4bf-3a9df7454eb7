import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  FlatList,
} from 'react-native';
import { useQuery } from '@tanstack/react-query';
import {
  fetchGeographies,
  fetchBedrooms,
  fetchListings,
  api
} from '@/lib/api';
import ListingCard from '@/components/ListingCard';
import Pagination from '@/components/Pagination';
import {
  RENT_SALE_OPTIONS,
  MIN_AREA_OPTIONS,
  MAX_AREA_OPTIONS,
  PRICE_MIN_OPTIONS,
  PRICE_MAX_OPTIONS,
  transformApiDataToDropdownOptions
} from '@/constants/dropdownOptions';

interface OfferNegotiationConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  minArea: string;
  maxArea: string;
  priceMin: string;
  priceMax: string;
  selectedProperties: string[];
}

interface OfferNegotiationFormProps {
  selectedProperties: string[];
  onPropertiesChange: (properties: string[]) => void;
  onSave: (config: { selectedProperties: string[] }) => void;
}

const ITEMS_PER_PAGE = 10;

export default function OfferNegotiationForm({
  selectedProperties,
  onPropertiesChange,
  onSave,
}: OfferNegotiationFormProps) {
  const [currentPage, setCurrentPage] = useState(1);

  const [offerConfig, setOfferConfig] = useState<OfferNegotiationConfig>({
    search: '',
    rentSale: '',
    towerBuilding: '',
    bedrooms: [],
    minArea: '',
    maxArea: '',
    priceMin: '',
    priceMax: '',
    selectedProperties: selectedProperties,
  });

  // Fetch dropdown data
  const { data: geographies = [] } = useQuery({
    queryKey: ['geographies'],
    queryFn: fetchGeographies,
  });

  const { data: bedrooms = [] } = useQuery({
    queryKey: ['bedrooms'],
    queryFn: fetchBedrooms,
  });

  // Transform API data to dropdown format
  const locationOptions = transformApiDataToDropdownOptions(geographies);
  const bedroomOptions = transformApiDataToDropdownOptions(bedrooms);

  // Get selected location object for towers query
  const selectedLocation = geographies.find((geo: any) => geo.id?.toString() === offerConfig.search);

  const { data: towers = [{ id: 'any', name: 'Any' }] } = useQuery({
    queryKey: ['towers', selectedLocation?.id],
    queryFn: async () => {
      if (!selectedLocation?.id) {
        return [{ id: 'any', name: 'Any' }];
      }
      try {
        const { data } = await api.get(`/geography/${selectedLocation.id}/towers`);
        return [{ id: 'any', name: 'Any' }, ...data];
      } catch (error) {
        console.error('Error fetching towers:', error);
        return [{ id: 'any', name: 'Any' }];
      }
    },
    enabled: true,
  });

  // Transform towers to dropdown format
  const towerBuildingOptions = transformApiDataToDropdownOptions(towers);

  useEffect(() => {
    setOfferConfig(prev => ({
      ...prev,
      selectedProperties: selectedProperties,
    }));
  }, [selectedProperties]);

  const updateOfferConfig = (field: keyof OfferNegotiationConfig, value: string | string[] | (string | number)[]) => {
    setOfferConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Create filters object for listings API
  const createListingFilters = (config: OfferNegotiationConfig) => {
    const filters: any = {};

    if (config.search) {
      filters.location = config.search;
    }

    if (config.rentSale) {
      const rentSaleMap: any = {
        'rent': 'rent',
        'sale': 'sale',
        'rent_sale': 'All'
      };
      filters.adType = rentSaleMap[config.rentSale] || 'All';
    }

    return filters;
  };

  // Check if any API-supported filters are applied
  const hasFilters = offerConfig?.search || offerConfig?.rentSale;

  // Check if any UI-only filters are applied
  const hasUIFilters = offerConfig?.towerBuilding ||
                      (offerConfig?.bedrooms && offerConfig.bedrooms.length > 0) ||
                      offerConfig?.priceMin || offerConfig?.priceMax;

  // Create query key without selectedProperties to avoid re-fetching on selection changes
  const offerConfigForQuery = {
    search: offerConfig.search,
    rentSale: offerConfig.rentSale,
    towerBuilding: offerConfig.towerBuilding,
    bedrooms: offerConfig.bedrooms,
    minArea: offerConfig.minArea,
    maxArea: offerConfig.maxArea,
    priceMin: offerConfig.priceMin,
    priceMax: offerConfig.priceMax,
  };

  // Fetch listings with pagination
  const { data: listingsData, isLoading: isLoadingListings } = useQuery({
    queryKey: ['offer-listings', offerConfigForQuery, currentPage],
    queryFn: () => {
      if (!hasFilters) {
        const params = {
          propertyType: undefined as number | undefined,
          adType: 'All' as string,
          vt: 'master' as string
        };
        return fetchListings(currentPage, params);
      }
      const filters = createListingFilters(offerConfig);
      const params = {
        ...filters,
        vt: 'master',
        adType: filters.adType || 'All'
      };
      return fetchListings(currentPage, params);
    },
    enabled: true,
  });

  // Apply client-side filtering for unsupported API filters
  const applyClientSideFilters = (listings: any[]) => {
    if (!hasUIFilters) return listings;

    return listings.filter(listing => {
      // Tower/Building filter
      if (offerConfig.towerBuilding && offerConfig.towerBuilding !== 'any') {
        if (listing.tower?.id?.toString() !== offerConfig.towerBuilding) {
          return false;
        }
      }

      // Bedrooms filter
      if (offerConfig.bedrooms && offerConfig.bedrooms.length > 0) {
        if (!offerConfig.bedrooms.includes(listing.bedrooms_no?.toString())) {
          return false;
        }
      }

      // Price filters
      if (offerConfig.priceMin) {
        const minPrice = parseInt(offerConfig.priceMin);
        const listingPrice = parseInt(listing.price?.replace(/[^0-9]/g, '') || '0');
        if (listingPrice < minPrice) {
          return false;
        }
      }

      if (offerConfig.priceMax) {
        const maxPrice = parseInt(offerConfig.priceMax);
        const listingPrice = parseInt(listing.price?.replace(/[^0-9]/g, '') || '0');
        if (listingPrice > maxPrice) {
          return false;
        }
      }

      return true;
    });
  };

  const rawListings = listingsData?.data || [];
  const listings = applyClientSideFilters(rawListings);
  const totalListings = listingsData?.total || 0;
  const totalPages = Math.ceil(totalListings / ITEMS_PER_PAGE);

  // Handle listing selection
  const toggleListingSelection = useCallback((listingId: string) => {
    const newSelectedProperties = offerConfig.selectedProperties.includes(listingId)
      ? offerConfig.selectedProperties.filter(id => id !== listingId)
      : [...offerConfig.selectedProperties, listingId];

    updateOfferConfig('selectedProperties', newSelectedProperties);
    onPropertiesChange(newSelectedProperties);
  }, [offerConfig.selectedProperties, onPropertiesChange]);

  const handleRemoveProperty = (propertyId: string) => {
    const updatedProperties = offerConfig.selectedProperties.filter(id => id !== propertyId);
    updateOfferConfig('selectedProperties', updatedProperties);
    onPropertiesChange(updatedProperties);
  };

  const handleSave = () => {
    onSave({
      selectedProperties: offerConfig.selectedProperties,
    });
  };

  // Render listing item
  const renderListingItem = useCallback(({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.listingItem}
      onPress={() => toggleListingSelection(item.id.toString())}
      activeOpacity={0.7}
    >
      <View style={styles.listingCheckboxContainer}>
        <View
          style={[
            styles.listingCheckbox,
            offerConfig.selectedProperties.includes(item.id.toString()) && styles.listingCheckboxSelected
          ]}
        >
          {offerConfig.selectedProperties.includes(item.id.toString()) && (
            <Text style={styles.listingCheckmark}>✓</Text>
          )}
        </View>
      </View>
      <View style={styles.listingCardContainer}>
        <ListingCard
          listing={item}
          disableNavigation={true}
          compact={true}
        />
      </View>
    </TouchableOpacity>
  ), [offerConfig.selectedProperties, toggleListingSelection]);

  const resetFilters = () => {
    setOfferConfig({
      search: '',
      rentSale: '',
      towerBuilding: '',
      bedrooms: [],
      minArea: '',
      maxArea: '',
      priceMin: '',
      priceMax: '',
      selectedProperties: [],
    });
    onPropertiesChange([]);
    setCurrentPage(1);
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Select Properties for Offer</Text>

      {/* Location Search */}
      <View style={styles.filterSection}>
        <Text style={styles.filterLabel}>Location</Text>
        <View style={styles.dropdownContainer}>
          {locationOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.dropdownOption,
                offerConfig.search === option.id && styles.dropdownOptionSelected
              ]}
              onPress={() => updateOfferConfig('search', option.id)}
            >
              <Text style={[
                styles.dropdownOptionText,
                offerConfig.search === option.id && styles.dropdownOptionTextSelected
              ]}>
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Rent/Sale Filter */}
      <View style={styles.filterSection}>
        <Text style={styles.filterLabel}>Rent/Sale</Text>
        <View style={styles.dropdownContainer}>
          {RENT_SALE_OPTIONS.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.dropdownOption,
                offerConfig.rentSale === option.id && styles.dropdownOptionSelected
              ]}
              onPress={() => updateOfferConfig('rentSale', option.id)}
            >
              <Text style={[
                styles.dropdownOptionText,
                offerConfig.rentSale === option.id && styles.dropdownOptionTextSelected
              ]}>
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Tower/Building Filter */}
      <View style={styles.filterSection}>
        <Text style={styles.filterLabel}>Tower/Building</Text>
        <View style={styles.dropdownContainer}>
          {towerBuildingOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.dropdownOption,
                offerConfig.towerBuilding === option.id && styles.dropdownOptionSelected
              ]}
              onPress={() => updateOfferConfig('towerBuilding', option.id)}
            >
              <Text style={[
                styles.dropdownOptionText,
                offerConfig.towerBuilding === option.id && styles.dropdownOptionTextSelected
              ]}>
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Reset Filters Button */}
      <TouchableOpacity style={styles.resetButton} onPress={resetFilters}>
        <Text style={styles.resetButtonText}>Reset Filters</Text>
      </TouchableOpacity>

      {/* Selected Properties Count */}
      <Text style={styles.selectedCount}>
        {offerConfig.selectedProperties.length} properties selected
      </Text>

      {/* Properties List */}
      <View style={styles.propertiesContainer}>
        {isLoadingListings ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading properties...</Text>
          </View>
        ) : listings.length > 0 ? (
          <>
            <Text style={styles.propertiesCount}>
              {listings.length} of {totalListings} properties
            </Text>
            <FlatList
              data={listings}
              keyExtractor={(item) => item.id.toString()}
              renderItem={renderListingItem}
              style={styles.propertiesList}
              showsVerticalScrollIndicator={false}
              scrollEnabled={false}
            />

            {/* Pagination */}
            {totalPages > 1 && (
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
            )}
          </>
        ) : (
          <View style={styles.noPropertiesContainer}>
            <Text style={styles.noPropertiesText}>No properties found</Text>
          </View>
        )}
      </View>

      {/* Selected Properties Display */}
      {offerConfig.selectedProperties.length > 0 && (
        <View style={styles.selectedPropertiesContainer}>
          <Text style={styles.selectedPropertiesTitle}>Selected Properties:</Text>
          <ScrollView style={styles.selectedPropertiesList} showsVerticalScrollIndicator={false}>
            {offerConfig.selectedProperties.map((propertyId, index) => {
              const property = rawListings.find(p => p.id.toString() === propertyId);
              return (
                <View key={propertyId} style={styles.selectedPropertyItem}>
                  <Text style={styles.selectedPropertyText}>
                    {property ? `${property.ref_no} - ${property.title || 'Property'}` : `Property ${index + 1}: ${propertyId}`}
                  </Text>
                  <TouchableOpacity
                    onPress={() => handleRemoveProperty(propertyId)}
                    style={styles.removePropertyButton}
                  >
                    <Text style={styles.removePropertyButtonText}>✕</Text>
                  </TouchableOpacity>
                </View>
              );
            })}
          </ScrollView>
        </View>
      )}

      {/* Save Button */}
      <TouchableOpacity
        style={[
          styles.saveButton,
          offerConfig.selectedProperties.length === 0 && styles.saveButtonDisabled
        ]}
        onPress={handleSave}
        disabled={offerConfig.selectedProperties.length === 0}
      >
        <Text style={[
          styles.saveButtonText,
          offerConfig.selectedProperties.length === 0 && styles.saveButtonTextDisabled
        ]}>
          Save Offer ({offerConfig.selectedProperties.length} properties)
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  // Filter Section styles
  filterSection: {
    marginBottom: 16,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  dropdownContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  dropdownOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#B89C4C',
    backgroundColor: 'transparent',
  },
  dropdownOptionSelected: {
    backgroundColor: '#B89C4C',
  },
  dropdownOptionText: {
    fontSize: 14,
    color: '#B89C4C',
    fontWeight: '500',
  },
  dropdownOptionTextSelected: {
    color: '#fff',
  },
  resetButton: {
    backgroundColor: '#EF4444',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginBottom: 16,
  },
  resetButtonText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '600',
  },
  selectedCount: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
    fontWeight: '500',
  },
  // Properties List styles
  propertiesContainer: {
    marginBottom: 16,
  },
  propertiesCount: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
    fontWeight: '500',
  },
  propertiesList: {
    maxHeight: 400,
  },
  listingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 8,
  },
  listingCheckboxContainer: {
    marginRight: 12,
  },
  listingCheckbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  listingCheckboxSelected: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  listingCheckmark: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  listingCardContainer: {
    flex: 1,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  noPropertiesContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  noPropertiesText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  // Selected Properties styles
  selectedPropertiesContainer: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    maxHeight: 150,
  },
  selectedPropertiesTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  selectedPropertiesList: {
    maxHeight: 100,
  },
  selectedPropertyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 6,
    padding: 8,
    marginBottom: 4,
  },
  selectedPropertyText: {
    fontSize: 12,
    color: '#1F2937',
    flex: 1,
  },
  removePropertyButton: {
    backgroundColor: '#EF4444',
    borderRadius: 4,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  removePropertyButtonText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: 'bold',
  },
  // Save Button styles
  saveButton: {
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginTop: 16,
  },
  saveButtonDisabled: {
    backgroundColor: '#E5E7EB',
  },
  saveButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '600',
  },
  saveButtonTextDisabled: {
    color: '#9CA3AF',
  },
});
