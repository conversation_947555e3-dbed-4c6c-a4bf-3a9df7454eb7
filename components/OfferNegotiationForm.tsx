import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  FlatList,
} from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { fetchListings } from '@/lib/api';
import ListingCard from '@/components/ListingCard';

interface OfferNegotiationFormProps {
  selectedProperties: string[];
  onPropertiesChange: (properties: string[]) => void;
  onSave: (config: { selectedProperties: string[] }) => void;
}

export default function OfferNegotiationForm({
  selectedProperties,
  onPropertiesChange,
  onSave,
}: OfferNegotiationFormProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const ITEMS_PER_PAGE = 10;

  // Get listings using React Query
  const { data, isLoading } = useQuery({
    queryKey: ['listings', { page: 1 }],
    queryFn: () => fetchListings(1, {}),
  });

  const listings = data?.data ?? [];

  // Filter listings based on search query (title and ref_no)
  const filteredListings = listings.filter(listing => {
    if (!searchQuery.trim()) return true;

    const query = searchQuery.toLowerCase();
    const title = listing.title?.toLowerCase() || '';
    const refNo = listing.ref_no?.toLowerCase() || '';

    return title.includes(query) || refNo.includes(query);
  });

  // Paginate filtered listings
  const paginatedListings = filteredListings.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const totalPages = Math.ceil(filteredListings.length / ITEMS_PER_PAGE);

  const handleToggleProperty = (propertyId: string) => {
    const isSelected = selectedProperties.includes(propertyId);
    if (isSelected) {
      const updatedProperties = selectedProperties.filter(id => id !== propertyId);
      onPropertiesChange(updatedProperties);
    } else {
      onPropertiesChange([...selectedProperties, propertyId]);
    }
  };

  const handleRemoveProperty = (propertyId: string) => {
    const updatedProperties = selectedProperties.filter(id => id !== propertyId);
    onPropertiesChange(updatedProperties);
  };

  const handleSave = () => {
    onSave({
      selectedProperties,
    });
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Select Properties for Offer</Text>

      {/* Search Input */}
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search by title or reference number..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Selected Properties Count */}
      <Text style={styles.selectedCount}>
        {selectedProperties.length} properties selected
      </Text>

      {/* Properties List */}
      <View style={styles.propertiesContainer}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading properties...</Text>
          </View>
        ) : filteredListings.length > 0 ? (
          <>
            <FlatList
              data={paginatedListings}
              keyExtractor={(item) => item.id.toString()}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.propertyItem}
                  onPress={() => handleToggleProperty(item.id.toString())}
                >
                  <View style={styles.checkboxContainer}>
                    <View style={[
                      styles.checkbox,
                      selectedProperties.includes(item.id.toString()) && styles.checkboxSelected
                    ]}>
                      {selectedProperties.includes(item.id.toString()) && (
                        <Text style={styles.checkmark}>✓</Text>
                      )}
                    </View>
                  </View>
                  <View style={styles.propertyCardContainer}>
                    <ListingCard
                      listing={item}
                      disableNavigation={true}
                      compact={true}
                    />
                  </View>
                </TouchableOpacity>
              )}
              style={styles.propertiesList}
              showsVerticalScrollIndicator={false}
            />

            {/* Pagination */}
            {totalPages > 1 && (
              <View style={styles.paginationContainer}>
                <TouchableOpacity
                  style={[styles.paginationButton, currentPage === 1 && styles.paginationButtonDisabled]}
                  onPress={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  <Text style={styles.paginationButtonText}>←</Text>
                </TouchableOpacity>

                <Text style={styles.paginationText}>
                  Page {currentPage} of {totalPages}
                </Text>

                <TouchableOpacity
                  style={[styles.paginationButton, currentPage === totalPages && styles.paginationButtonDisabled]}
                  onPress={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  <Text style={styles.paginationButtonText}>→</Text>
                </TouchableOpacity>
              </View>
            )}
          </>
        ) : (
          <View style={styles.noPropertiesContainer}>
            <Text style={styles.noPropertiesText}>
              {searchQuery ? 'No properties found matching your search' : 'No properties available'}
            </Text>
          </View>
        )}
      </View>

      {/* Selected Properties Display */}
      {selectedProperties.length > 0 && (
        <View style={styles.selectedPropertiesContainer}>
          <Text style={styles.selectedPropertiesTitle}>Selected Properties:</Text>
          <ScrollView style={styles.selectedPropertiesList} showsVerticalScrollIndicator={false}>
            {selectedProperties.map((propertyId, index) => {
              const property = listings.find(p => p.id.toString() === propertyId);
              return (
                <View key={propertyId} style={styles.selectedPropertyItem}>
                  <Text style={styles.selectedPropertyText}>
                    {property ? `${property.ref_no} - ${property.title}` : `Property ${index + 1}: ${propertyId}`}
                  </Text>
                  <TouchableOpacity
                    onPress={() => handleRemoveProperty(propertyId)}
                    style={styles.removePropertyButton}
                  >
                    <Text style={styles.removePropertyButtonText}>✕</Text>
                  </TouchableOpacity>
                </View>
              );
            })}
          </ScrollView>
        </View>
      )}

      {/* Save Button */}
      <TouchableOpacity
        style={[
          styles.saveButton,
          selectedProperties.length === 0 && styles.saveButtonDisabled
        ]}
        onPress={handleSave}
        disabled={selectedProperties.length === 0}
      >
        <Text style={[
          styles.saveButtonText,
          selectedProperties.length === 0 && styles.saveButtonTextDisabled
        ]}>
          Save Offer ({selectedProperties.length} properties)
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  // Search Input styles
  searchContainer: {
    marginBottom: 16,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#F9FAFB',
  },
  selectedCount: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
    fontWeight: '500',
  },
  // Properties List styles
  propertiesContainer: {
    flex: 1,
    marginBottom: 16,
  },
  propertiesList: {
    maxHeight: 300,
  },
  propertyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 8,
  },
  checkboxContainer: {
    marginRight: 12,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  checkboxSelected: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  checkmark: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  propertyCardContainer: {
    flex: 1,
  },
  // Pagination styles
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
    paddingVertical: 8,
  },
  paginationButton: {
    backgroundColor: '#B89C4C',
    borderRadius: 6,
    width: 36,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8,
  },
  paginationButtonDisabled: {
    backgroundColor: '#E5E7EB',
  },
  paginationButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  paginationText: {
    fontSize: 14,
    color: '#6B7280',
    marginHorizontal: 16,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  noPropertiesContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  noPropertiesText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  // Selected Properties styles
  selectedPropertiesContainer: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    maxHeight: 150,
  },
  selectedPropertiesTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  selectedPropertiesList: {
    maxHeight: 100,
  },
  selectedPropertyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 6,
    padding: 8,
    marginBottom: 4,
  },
  selectedPropertyText: {
    fontSize: 12,
    color: '#1F2937',
    flex: 1,
  },
  removePropertyButton: {
    backgroundColor: '#EF4444',
    borderRadius: 4,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  removePropertyButtonText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: 'bold',
  },
  // Save Button styles
  saveButton: {
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginTop: 16,
  },
  saveButtonDisabled: {
    backgroundColor: '#E5E7EB',
  },
  saveButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '600',
  },
  saveButtonTextDisabled: {
    color: '#9CA3AF',
  },
});
