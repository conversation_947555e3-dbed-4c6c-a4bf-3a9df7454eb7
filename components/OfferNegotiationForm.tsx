import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { fetchListings } from '@/lib/api';
import SearchBar from '@/components/SearchBar';
import ListingCard from '@/components/ListingCard';
import Pagination from '@/components/Pagination';
import SelectedPropertiesPills from '@/components/SelectedPropertiesPills';

interface OfferNegotiationFormProps {
  selectedProperties: string[];
  onPropertiesChange: (properties: string[]) => void;
}

const ITEMS_PER_PAGE = 10;
const SEARCH_RESULTS_PER_PAGE = 20;

export default function OfferNegotiationForm({
  selectedProperties,
  onPropertiesChange,
}: OfferNegotiationFormProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [allLoadedProperties, setAllLoadedProperties] = useState<any[]>([]);

  // Debounce search query to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300); // 300ms delay

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Reset to page 1 when search query changes
  useEffect(() => {
    if (debouncedSearchQuery.trim()) {
      setCurrentPage(1);
    }
  }, [debouncedSearchQuery]);

  // Fetch properties - different strategy for search vs pagination (exact copy from PropertySearchModal)
  const { data: listingsData, isLoading } = useQuery({
    queryKey: ['property-search', debouncedSearchQuery.trim() ? 'search' : currentPage, debouncedSearchQuery.trim()],
    queryFn: async () => {
      const params = {
        propertyType: undefined as number | undefined,
        adType: 'All' as string,
        vt: 'master' as string
      };

      // If searching, fetch fewer pages for better performance
      if (debouncedSearchQuery.trim()) {
        const searchResults = [];
        let totalFound = 0;

        // Fetch first 5 pages (50 properties) for faster search
        const promises = [];
        for (let page = 1; page <= 5; page++) {
          promises.push(fetchListings(page, params));
        }

        const results = await Promise.all(promises);

        // Combine all results
        for (const result of results) {
          if (result?.data) {
            searchResults.push(...result.data);
            totalFound = result.total || totalFound; // Use the total from any response
          }
        }

        return {
          data: searchResults,
          total: totalFound
        };
      } else {
        // Normal pagination when not searching
        return fetchListings(currentPage, params);
      }
    },
    enabled: true,
  });

  const rawListings = listingsData?.data || [];
  const totalListings = listingsData?.total || 0;
  const totalPages = Math.ceil(totalListings / ITEMS_PER_PAGE);

  // Update allLoadedProperties whenever new data is fetched
  useEffect(() => {
    if (rawListings.length > 0) {
      setAllLoadedProperties(prev => {
        const existingIds = new Set(prev.map(p => p.id));
        const newProperties = rawListings.filter(p => !existingIds.has(p.id));
        return [...prev, ...newProperties];
      });
    }
  }, [rawListings]);

  // Filter listings based on search query (title and ref_no) - exact copy from PropertySearchModal
  const allFilteredListings = rawListings.filter(listing => {
    if (!searchQuery.trim()) return true;

    const query = searchQuery.toLowerCase().trim();
    const title = listing.title?.toLowerCase() || '';
    const refNo = listing.ref_no?.toLowerCase() || '';

    return title.includes(query) || refNo.includes(query);
  });

  // Paginate search results if needed - exact copy from PropertySearchModal
  const totalFilteredListings = allFilteredListings.length;
  const totalFilteredPages = Math.ceil(totalFilteredListings / SEARCH_RESULTS_PER_PAGE);

  // Get current page of filtered results
  const startIndex = searchQuery.trim()
    ? (currentPage - 1) * SEARCH_RESULTS_PER_PAGE
    : 0;
  const endIndex = searchQuery.trim()
    ? startIndex + SEARCH_RESULTS_PER_PAGE
    : allFilteredListings.length;

  const filteredListings = searchQuery.trim()
    ? allFilteredListings.slice(startIndex, endIndex)
    : allFilteredListings;

  const handleToggleProperty = useCallback((propertyId: string) => {
    onPropertiesChange(
      selectedProperties.includes(propertyId)
        ? selectedProperties.filter(id => id !== propertyId)
        : [...selectedProperties, propertyId]
    );
  }, [selectedProperties, onPropertiesChange]);



  // Render listing item
  const renderListingItem = useCallback(({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.listingItem}
      onPress={() => handleToggleProperty(item.id.toString())}
      activeOpacity={0.7}
    >
      <View style={styles.listingCheckboxContainer}>
        <View
          style={[
            styles.listingCheckbox,
            selectedProperties.includes(item.id.toString()) && styles.listingCheckboxSelected
          ]}
        >
          {selectedProperties.includes(item.id.toString()) && (
            <Text style={styles.listingCheckmark}>✓</Text>
          )}
        </View>
      </View>
      <View style={styles.listingCardContainer}>
        <ListingCard
          listing={item}
          disableNavigation={true}
          compact={true}
        />
      </View>
    </TouchableOpacity>
  ), [selectedProperties, handleToggleProperty]);

  const getItemLayout = useCallback((_: any, index: number) => ({
    length: 90,
    offset: 90 * index,
    index,
  }), []);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Select Properties for Offer</Text>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <SearchBar
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Search by title or ref no..."
          isLoading={isLoading}
        />
      </View>

      {/* Selected Properties Pills - Fixed position under search bar */}
      {selectedProperties.length > 0 && (
        <View style={styles.selectedPillsContainer}>
          <SelectedPropertiesPills
            selectedProperties={selectedProperties}
            listings={allLoadedProperties}
            onRemoveProperty={handleToggleProperty}
            showNavigationOnPress={false}
            title="Selected"
          />
        </View>
      )}

      {/* Content */}
      <View style={styles.contentContainer}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>
              {searchQuery.trim() ? 'Searching all properties...' : 'Loading properties...'}
            </Text>
          </View>
        ) : filteredListings.length > 0 ? (
          <>
            {/* <Text style={styles.propertiesCount}>
              {searchQuery.trim()
                ? `${totalFilteredListings} properties found for "${searchQuery}"${totalFilteredListings > SEARCH_RESULTS_PER_PAGE ? ` (showing ${filteredListings.length})` : ''}`
                : `${filteredListings.length} properties`
              }
            </Text> */}
            <FlatList
              data={filteredListings}
              keyExtractor={(item) => item.id.toString()}
              renderItem={renderListingItem}
              getItemLayout={getItemLayout}
              style={styles.listingsList}
              showsVerticalScrollIndicator={false}
              removeClippedSubviews={true}
              maxToRenderPerBatch={10}
              windowSize={10}

              ListFooterComponent={() => {
                // Show pagination for normal browsing or search results with multiple pages
                const showPagination = searchQuery.trim()
                  ? totalFilteredPages > 1
                  : totalPages > 1;
                const pagesToShow = searchQuery.trim() ? totalFilteredPages : totalPages;

                return showPagination ? (
                  <View style={styles.paginationContainer}>
                    <Pagination
                      currentPage={currentPage}
                      totalPages={pagesToShow}
                      onPageChange={setCurrentPage}
                    />
                  </View>
                ) : null;
              }}
            />
          </>
        ) : (
          <View style={styles.noPropertiesContainer}>
            <Text style={styles.noPropertiesText}>
              {searchQuery ? 'No properties found matching your search' : 'No properties found'}
            </Text>
          </View>
        )}
      </View>


    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  selectedPillsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    backgroundColor: '#F9FAFB',
  },
  contentContainer: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  propertiesCount: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
  },
  listingsList: {
    flex: 1,
  },
  paginationContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
  noPropertiesContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noPropertiesText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  listingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 8,
  },
  listingCheckboxContainer: {
    marginRight: 12,
  },
  listingCheckbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  listingCheckboxSelected: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  listingCheckmark: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  listingCardContainer: {
    flex: 1,
  },
});
