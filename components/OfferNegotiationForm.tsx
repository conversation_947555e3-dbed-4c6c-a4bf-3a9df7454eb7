import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import PropertySearchModal from '@/components/PropertySearchModal';

interface OfferNegotiationFormProps {
  selectedProperties: string[];
  onPropertiesChange: (properties: string[]) => void;
  onSave: (config: { selectedProperties: string[] }) => void;
}

export default function OfferNegotiationForm({
  selectedProperties,
  onPropertiesChange,
  onSave,
}: OfferNegotiationFormProps) {
  const [showPropertySearchModal, setShowPropertySearchModal] = useState(false);

  const handleSelectPropertiesFromSearch = (selectedPropertyIds: string[]) => {
    onPropertiesChange(selectedPropertyIds);
    setShowPropertySearchModal(false);
  };

  const handleRemoveProperty = (propertyId: string) => {
    const updatedProperties = selectedProperties.filter(id => id !== propertyId);
    onPropertiesChange(updatedProperties);
  };

  const handleSave = () => {
    onSave({
      selectedProperties,
    });
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Select Properties for Offer</Text>

      {/* Search Properties Button */}
      <TouchableOpacity 
        style={styles.searchPropertiesButton}
        onPress={() => setShowPropertySearchModal(true)}
      >
        <Text style={styles.searchPropertiesButtonText}>
          Search Properties ({selectedProperties.length} selected)
        </Text>
      </TouchableOpacity>

      {/* Selected Properties Display */}
      {selectedProperties.length > 0 && (
        <View style={styles.selectedPropertiesContainer}>
          <Text style={styles.selectedPropertiesTitle}>Selected Properties:</Text>
          {selectedProperties.map((propertyId, index) => (
            <View key={propertyId} style={styles.selectedPropertyItem}>
              <Text style={styles.selectedPropertyText}>
                Property {index + 1}: {propertyId}
              </Text>
              <TouchableOpacity 
                onPress={() => handleRemoveProperty(propertyId)}
                style={styles.removePropertyButton}
              >
                <Text style={styles.removePropertyButtonText}>✕</Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>
      )}

      {/* Save Button */}
      <TouchableOpacity 
        style={[
          styles.saveButton,
          selectedProperties.length === 0 && styles.saveButtonDisabled
        ]}
        onPress={handleSave}
        disabled={selectedProperties.length === 0}
      >
        <Text style={[
          styles.saveButtonText,
          selectedProperties.length === 0 && styles.saveButtonTextDisabled
        ]}>
          Save Offer ({selectedProperties.length} properties)
        </Text>
      </TouchableOpacity>

      {/* Property Search Modal */}
      <PropertySearchModal
        visible={showPropertySearchModal}
        onClose={() => setShowPropertySearchModal(false)}
        onSelectProperties={handleSelectPropertiesFromSearch}
        initialSelectedProperties={selectedProperties}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  // Search Properties Button styles
  searchPropertiesButton: {
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginBottom: 16,
  },
  searchPropertiesButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '600',
  },
  // Selected Properties styles
  selectedPropertiesContainer: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  selectedPropertiesTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  selectedPropertyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 6,
    padding: 8,
    marginBottom: 4,
  },
  selectedPropertyText: {
    fontSize: 14,
    color: '#1F2937',
    flex: 1,
  },
  removePropertyButton: {
    backgroundColor: '#EF4444',
    borderRadius: 4,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  removePropertyButtonText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: 'bold',
  },
  // Save Button styles
  saveButton: {
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginTop: 16,
  },
  saveButtonDisabled: {
    backgroundColor: '#E5E7EB',
  },
  saveButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '600',
  },
  saveButtonTextDisabled: {
    color: '#9CA3AF',
  },
});
