import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  FlatList,
} from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { fetchListings } from '@/lib/api';
import SearchBar from '@/components/SearchBar';
import ListingCard from '@/components/ListingCard';
import Pagination from '@/components/Pagination';
import SelectedPropertiesPills from '@/components/SelectedPropertiesPills';

interface OfferNegotiationConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  priceMin: string;
  priceMax: string;
  selectedProperties: string[];
}

interface OfferNegotiationConfigFormProps {
  offerNegotiationConfig: OfferNegotiationConfig;
  updateOfferNegotiationConfig: (field: keyof OfferNegotiationConfig, value: string | string[]) => void;
  locationOptions: Array<{ id: number; label: string }>;
  finalTowerOptions: Array<{ id: number; label: string }>;
  bedroomOptions: Array<{ id: number; label: string }>;
  rentSaleOptions: Array<{ id: string; label: string }>;
  priceMinOptions: Array<{ id: string; label: string }>;
  priceMaxOptions: Array<{ id: string; label: string }>;
  onSave: (config: OfferNegotiationConfig) => void;
}

const ITEMS_PER_PAGE = 10;

export default function OfferNegotiationConfigForm({
  offerNegotiationConfig,
  updateOfferNegotiationConfig,
  locationOptions,
  finalTowerOptions,
  bedroomOptions,
  rentSaleOptions,
  priceMinOptions,
  priceMaxOptions,
  onSave,
}: OfferNegotiationConfigFormProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);

  // Debounce search query to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300); // 300ms delay

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Reset to page 1 when search query changes
  useEffect(() => {
    if (debouncedSearchQuery.trim()) {
      setCurrentPage(1);
    }
  }, [debouncedSearchQuery]);

  // Fetch properties - different strategy for search vs pagination
  const { data: listingsData, isLoading } = useQuery({
    queryKey: ['offer-property-search', debouncedSearchQuery.trim() ? 'search' : currentPage, debouncedSearchQuery.trim()],
    queryFn: async () => {
      const params = {
        propertyType: undefined as number | undefined,
        adType: 'All' as string,
        vt: 'master' as string
      };

      // If searching, fetch fewer pages for better performance
      if (debouncedSearchQuery.trim()) {
        const searchResults = [];
        let totalFound = 0;

        // Fetch first 5 pages (50 properties) for faster search
        const promises = [];
        for (let page = 1; page <= 5; page++) {
          promises.push(fetchListings(page, params));
        }

        const results = await Promise.all(promises);

        // Combine all results
        for (const result of results) {
          if (result?.data) {
            searchResults.push(...result.data);
            totalFound = result.total || totalFound; // Use the total from any response
          }
        }

        return {
          data: searchResults,
          total: totalFound
        };
      } else {
        // Normal pagination when not searching
        return fetchListings(currentPage, params);
      }
    },
  });

  const rawListings = listingsData?.data || [];
  const totalListings = listingsData?.total || 0;
  const totalPages = Math.ceil(totalListings / ITEMS_PER_PAGE);

  // Filter listings based on search query (title and ref_no)
  const allFilteredListings = rawListings.filter(listing => {
    if (!searchQuery.trim()) return true;

    const query = searchQuery.toLowerCase().trim();
    const title = listing.title?.toLowerCase() || '';
    const refNo = listing.ref_no?.toLowerCase() || '';

    return title.includes(query) || refNo.includes(query);
  });

  // Paginate search results if needed
  const SEARCH_RESULTS_PER_PAGE = 20;
  const totalFilteredListings = allFilteredListings.length;
  const totalFilteredPages = Math.ceil(totalFilteredListings / SEARCH_RESULTS_PER_PAGE);

  // Get current page of filtered results
  const startIndex = searchQuery.trim()
    ? (currentPage - 1) * SEARCH_RESULTS_PER_PAGE
    : 0;
  const endIndex = searchQuery.trim()
    ? startIndex + SEARCH_RESULTS_PER_PAGE
    : allFilteredListings.length;

  const filteredListings = searchQuery.trim()
    ? allFilteredListings.slice(startIndex, endIndex)
    : allFilteredListings;

  const handleToggleProperty = useCallback((propertyId: string) => {
    const currentSelected = offerNegotiationConfig.selectedProperties || [];
    const newSelected = currentSelected.includes(propertyId)
      ? currentSelected.filter(id => id !== propertyId)
      : [...currentSelected, propertyId];
    updateOfferNegotiationConfig('selectedProperties', newSelected);
  }, [offerNegotiationConfig.selectedProperties, updateOfferNegotiationConfig]);

  const renderListingItem = useCallback(({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.listingItem}
      onPress={() => handleToggleProperty(item.id.toString())}
      activeOpacity={0.7}
    >
      <View style={styles.listingCheckboxContainer}>
        <View
          style={[
            styles.listingCheckbox,
            (offerNegotiationConfig.selectedProperties || []).includes(item.id.toString()) && styles.listingCheckboxSelected
          ]}
        >
          {(offerNegotiationConfig.selectedProperties || []).includes(item.id.toString()) && (
            <Text style={styles.listingCheckmark}>✓</Text>
          )}
        </View>
      </View>
      <View style={styles.listingCardContainer}>
        <ListingCard
          listing={item}
          disableNavigation={true}
          compact={true}
        />
      </View>
    </TouchableOpacity>
  ), [offerNegotiationConfig.selectedProperties, handleToggleProperty]);

  const getItemLayout = useCallback((_: any, index: number) => ({
    length: 90,
    offset: 90 * index,
    index,
  }), []);

  return (
    <ScrollView style={styles.mainContainer} showsVerticalScrollIndicator={false}>
      <View style={styles.configContainer}>
        <Text style={styles.configTitle}>Lead configuration</Text>

        {/* Property Search Section */}
        <View style={styles.propertySearchSection}>
          <Text style={styles.sectionTitle}>Property Search</Text>

          {/* Search Bar */}
          <View style={styles.searchContainer}>
            <SearchBar
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Search by title or ref no..."
              isLoading={isLoading}
            />
          </View>

          {/* Selected Properties Pills */}
          <SelectedPropertiesPills
            selectedProperties={offerNegotiationConfig.selectedProperties || []}
            listings={rawListings}
            onRemoveProperty={handleToggleProperty}
            showNavigationOnPress={true}
            title="Selected Properties"
          />

          {/* Property Results */}
          <View style={styles.propertyResults}>
            {isLoading ? (
              <View style={styles.loadingContainer}>
                <Text style={styles.loadingText}>
                  {searchQuery.trim() ? 'Searching all properties...' : 'Loading properties...'}
                </Text>
              </View>
            ) : filteredListings.length > 0 ? (
              <>
                <Text style={styles.propertiesCount}>
                  {searchQuery.trim()
                    ? `${totalFilteredListings} properties found for "${searchQuery}"${totalFilteredListings > SEARCH_RESULTS_PER_PAGE ? ` (showing ${filteredListings.length})` : ''}`
                    : `${filteredListings.length} properties`
                  }
                </Text>
                <FlatList
                  data={filteredListings}
                  keyExtractor={(item) => item.id.toString()}
                  renderItem={renderListingItem}
                  getItemLayout={getItemLayout}
                  style={styles.propertiesList}
                  showsVerticalScrollIndicator={false}
                  removeClippedSubviews={true}
                  maxToRenderPerBatch={10}
                  windowSize={10}
                  nestedScrollEnabled={true}
                  ListFooterComponent={() => {
                    // Show pagination for normal browsing or search results with multiple pages
                    const showPagination = searchQuery.trim()
                      ? totalFilteredPages > 1
                      : totalPages > 1;
                    const pagesToShow = searchQuery.trim() ? totalFilteredPages : totalPages;

                    return showPagination ? (
                      <View style={styles.paginationContainer}>
                        <Pagination
                          currentPage={currentPage}
                          totalPages={pagesToShow}
                          onPageChange={setCurrentPage}
                        />
                      </View>
                    ) : null;
                  }}
                />
              </>
            ) : (
              <View style={styles.noPropertiesContainer}>
                <Text style={styles.noPropertiesText}>
                  {searchQuery ? 'No properties found matching your search' : 'No properties found'}
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Filters Section */}
        <View style={styles.filtersSection}>
          <Text style={styles.sectionTitle}>Filters</Text>

          {/* Location Row */}
      <View style={styles.fullWidthContainer}>
        <Text style={styles.inputLabel}>Location</Text>
        <ScrollView
          horizontal
          style={styles.pillScrollContainer}
          contentContainerStyle={styles.pillScrollContent}
          showsHorizontalScrollIndicator={false}
        >
          {locationOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.filterPill,
                offerNegotiationConfig.search === option.id.toString() && styles.selectedFilterPill
              ]}
              onPress={() => {
                const newValue = offerNegotiationConfig.search === option.id.toString() ? '' : option.id.toString();
                updateOfferNegotiationConfig('search', newValue);
                updateOfferNegotiationConfig('towerBuilding', '');
              }}
            >
              <Text
                style={[
                  styles.filterPillText,
                  offerNegotiationConfig.search === option.id.toString() && styles.selectedFilterPillText
                ]}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Tower/Building Row - Only show when location is selected */}
      {offerNegotiationConfig.search && (
        <View style={styles.fullWidthContainer}>
          <Text style={styles.inputLabel}>Tower/Building</Text>
          <ScrollView
            horizontal
            style={styles.pillScrollContainer}
            contentContainerStyle={styles.pillScrollContent}
            showsHorizontalScrollIndicator={false}
          >
            {finalTowerOptions.map((option) => {
              const isSelected = offerNegotiationConfig.towerBuilding === option.id.toString();
              return (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.filterPill,
                    isSelected && styles.selectedFilterPill
                  ]}
                  onPress={() => {
                    const newValue = isSelected ? '' : option.id.toString();
                    updateOfferNegotiationConfig('towerBuilding', newValue);
                  }}
                >
                  <Text
                    style={[
                      styles.filterPillText,
                      isSelected && styles.selectedFilterPillText
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </ScrollView>
        </View>
      )}

      {/* Rent/Sale Row */}
      <View style={styles.fullWidthContainer}>
        <Text style={styles.inputLabel}>Rent/Sale</Text>
        <ScrollView
          horizontal
          style={styles.pillScrollContainer}
          contentContainerStyle={styles.pillScrollContent}
          showsHorizontalScrollIndicator={false}
        >
          {rentSaleOptions.map((option) => {
            const isSelected = offerNegotiationConfig.rentSale === option.id.toString();
            return (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.filterPill,
                  isSelected && styles.selectedFilterPill
                ]}
                onPress={() => {
                  const newValue = isSelected ? '' : option.id.toString();
                  updateOfferNegotiationConfig('rentSale', newValue);
                }}
              >
                <Text
                  style={[
                    styles.filterPillText,
                    isSelected && styles.selectedFilterPillText
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>

      {/* Bedrooms Row */}
      <View style={styles.fullWidthContainer}>
        <Text style={styles.inputLabel}>Bedrooms</Text>
        <ScrollView
          horizontal
          style={styles.pillScrollContainer}
          contentContainerStyle={styles.pillScrollContent}
          showsHorizontalScrollIndicator={false}
        >
          {bedroomOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.filterPill,
                offerNegotiationConfig.bedrooms.includes(option.id.toString()) && styles.selectedFilterPill
              ]}
              onPress={() => {
                const currentBedrooms = offerNegotiationConfig.bedrooms;
                const bedroomId = option.id.toString();
                const newBedrooms = currentBedrooms.includes(bedroomId)
                  ? currentBedrooms.filter(id => id !== bedroomId)
                  : [...currentBedrooms, bedroomId];
                updateOfferNegotiationConfig('bedrooms', newBedrooms);
              }}
            >
              <Text
                style={[
                  styles.filterPillText,
                  offerNegotiationConfig.bedrooms.includes(option.id.toString()) && styles.selectedFilterPillText
                ]}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Price Min Row */}
      <View style={styles.fullWidthContainer}>
        <Text style={styles.inputLabel}>Price Min</Text>
        <ScrollView
          horizontal
          style={styles.pillScrollContainer}
          contentContainerStyle={styles.pillScrollContent}
          showsHorizontalScrollIndicator={false}
        >
          {priceMinOptions.map((option) => {
            const isSelected = offerNegotiationConfig.priceMin === option.id.toString();
            return (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.filterPill,
                  isSelected && styles.selectedFilterPill
                ]}
                onPress={() => {
                  const newValue = isSelected ? '' : option.id.toString();
                  updateOfferNegotiationConfig('priceMin', newValue);
                }}
              >
                <Text
                  style={[
                    styles.filterPillText,
                    isSelected && styles.selectedFilterPillText
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>

      {/* Price Max Row */}
      <View style={styles.fullWidthContainer}>
        <Text style={styles.inputLabel}>Price Max</Text>
        <ScrollView
          horizontal
          style={styles.pillScrollContainer}
          contentContainerStyle={styles.pillScrollContent}
          showsHorizontalScrollIndicator={false}
        >
          {priceMaxOptions.map((option) => {
            const isSelected = offerNegotiationConfig.priceMax === option.id.toString();
            return (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.filterPill,
                  isSelected && styles.selectedFilterPill
                ]}
                onPress={() => {
                  const newValue = isSelected ? '' : option.id.toString();
                  updateOfferNegotiationConfig('priceMax', newValue);
                }}
              >
                <Text
                  style={[
                    styles.filterPillText,
                    isSelected && styles.selectedFilterPillText
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>

          {/* Save Button */}
          <TouchableOpacity
            style={styles.saveButton}
            onPress={() => onSave(offerNegotiationConfig)}
          >
            <Text style={styles.saveButtonText}>Save</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  configContainer: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  configTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  propertySearchSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  filtersSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  searchContainer: {
    marginBottom: 12,
  },
  propertyResults: {
    marginTop: 8,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: '#6B7280',
  },
  propertiesCount: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
  },
  propertiesList: {
    maxHeight: 400,
  },
  paginationContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
  noPropertiesContainer: {
    padding: 20,
    alignItems: 'center',
  },
  noPropertiesText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
  listingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 8,
  },
  listingCheckboxContainer: {
    marginRight: 12,
  },
  listingCheckbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  listingCheckboxSelected: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  listingCheckmark: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  listingCardContainer: {
    flex: 1,
  },
  fullWidthContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  pillScrollContainer: {
    flexDirection: 'row',
  },
  pillScrollContent: {
    paddingRight: 16,
  },
  filterPill: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#B89C4C',
    backgroundColor: '#fff',
    marginRight: 8,
    minHeight: 32,
    justifyContent: 'center',
  },
  selectedFilterPill: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  filterPillText: {
    fontSize: 12,
    color: '#B89C4C',
    fontWeight: '500',
    textAlign: 'center',
  },
  selectedFilterPillText: {
    color: '#fff',
  },
  saveButton: {
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 16,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
});
