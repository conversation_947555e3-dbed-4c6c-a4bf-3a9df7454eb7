import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';

interface OfferNegotiationConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  priceMin: string;
  priceMax: string;
}

interface OfferNegotiationConfigFormProps {
  offerNegotiationConfig: OfferNegotiationConfig;
  updateOfferNegotiationConfig: (field: keyof OfferNegotiationConfig, value: string | string[]) => void;
  locationOptions: Array<{ id: number; label: string }>;
  finalTowerOptions: Array<{ id: number; label: string }>;
  bedroomOptions: Array<{ id: number; label: string }>;
  rentSaleOptions: Array<{ id: string; label: string }>;
  priceMinOptions: Array<{ id: string; label: string }>;
  priceMaxOptions: Array<{ id: string; label: string }>;
  onSave: (config: OfferNegotiationConfig) => void;
}

export default function OfferNegotiationConfigForm({
  offerNegotiationConfig,
  updateOfferNegotiationConfig,
  locationOptions,
  finalTowerOptions,
  bedroomOptions,
  rentSaleOptions,
  priceMinOptions,
  priceMaxOptions,
  onSave,
}: OfferNegotiationConfigFormProps) {
  return (
    <View style={styles.configContainer}>
      <Text style={styles.configTitle}>Lead configuration</Text>

      {/* Location Row */}
      <View style={styles.fullWidthContainer}>
        <Text style={styles.inputLabel}>Location</Text>
        <ScrollView
          horizontal
          style={styles.pillScrollContainer}
          contentContainerStyle={styles.pillScrollContent}
          showsHorizontalScrollIndicator={false}
        >
          {locationOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.filterPill,
                offerNegotiationConfig.search === option.id.toString() && styles.selectedFilterPill
              ]}
              onPress={() => {
                const newValue = offerNegotiationConfig.search === option.id.toString() ? '' : option.id.toString();
                updateOfferNegotiationConfig('search', newValue);
                updateOfferNegotiationConfig('towerBuilding', '');
              }}
            >
              <Text
                style={[
                  styles.filterPillText,
                  offerNegotiationConfig.search === option.id.toString() && styles.selectedFilterPillText
                ]}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Tower/Building Row - Only show when location is selected */}
      {offerNegotiationConfig.search && (
        <View style={styles.fullWidthContainer}>
          <Text style={styles.inputLabel}>Tower/Building</Text>
          <ScrollView
            horizontal
            style={styles.pillScrollContainer}
            contentContainerStyle={styles.pillScrollContent}
            showsHorizontalScrollIndicator={false}
          >
            {finalTowerOptions.map((option) => {
              const isSelected = offerNegotiationConfig.towerBuilding === option.id.toString();
              return (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.filterPill,
                    isSelected && styles.selectedFilterPill
                  ]}
                  onPress={() => {
                    const newValue = isSelected ? '' : option.id.toString();
                    updateOfferNegotiationConfig('towerBuilding', newValue);
                  }}
                >
                  <Text
                    style={[
                      styles.filterPillText,
                      isSelected && styles.selectedFilterPillText
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </ScrollView>
        </View>
      )}

      {/* Rent/Sale Row */}
      <View style={styles.fullWidthContainer}>
        <Text style={styles.inputLabel}>Rent/Sale</Text>
        <ScrollView
          horizontal
          style={styles.pillScrollContainer}
          contentContainerStyle={styles.pillScrollContent}
          showsHorizontalScrollIndicator={false}
        >
          {rentSaleOptions.map((option) => {
            const isSelected = offerNegotiationConfig.rentSale === option.id.toString();
            return (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.filterPill,
                  isSelected && styles.selectedFilterPill
                ]}
                onPress={() => {
                  const newValue = isSelected ? '' : option.id.toString();
                  updateOfferNegotiationConfig('rentSale', newValue);
                }}
              >
                <Text
                  style={[
                    styles.filterPillText,
                    isSelected && styles.selectedFilterPillText
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>

      {/* Bedrooms Row */}
      <View style={styles.fullWidthContainer}>
        <Text style={styles.inputLabel}>Bedrooms</Text>
        <ScrollView
          horizontal
          style={styles.pillScrollContainer}
          contentContainerStyle={styles.pillScrollContent}
          showsHorizontalScrollIndicator={false}
        >
          {bedroomOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.filterPill,
                offerNegotiationConfig.bedrooms.includes(option.id.toString()) && styles.selectedFilterPill
              ]}
              onPress={() => {
                const currentBedrooms = offerNegotiationConfig.bedrooms;
                const bedroomId = option.id.toString();
                const newBedrooms = currentBedrooms.includes(bedroomId)
                  ? currentBedrooms.filter(id => id !== bedroomId)
                  : [...currentBedrooms, bedroomId];
                updateOfferNegotiationConfig('bedrooms', newBedrooms);
              }}
            >
              <Text
                style={[
                  styles.filterPillText,
                  offerNegotiationConfig.bedrooms.includes(option.id.toString()) && styles.selectedFilterPillText
                ]}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Price Min Row */}
      <View style={styles.fullWidthContainer}>
        <Text style={styles.inputLabel}>Price Min</Text>
        <ScrollView
          horizontal
          style={styles.pillScrollContainer}
          contentContainerStyle={styles.pillScrollContent}
          showsHorizontalScrollIndicator={false}
        >
          {priceMinOptions.map((option) => {
            const isSelected = offerNegotiationConfig.priceMin === option.id.toString();
            return (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.filterPill,
                  isSelected && styles.selectedFilterPill
                ]}
                onPress={() => {
                  const newValue = isSelected ? '' : option.id.toString();
                  updateOfferNegotiationConfig('priceMin', newValue);
                }}
              >
                <Text
                  style={[
                    styles.filterPillText,
                    isSelected && styles.selectedFilterPillText
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>

      {/* Price Max Row */}
      <View style={styles.fullWidthContainer}>
        <Text style={styles.inputLabel}>Price Max</Text>
        <ScrollView
          horizontal
          style={styles.pillScrollContainer}
          contentContainerStyle={styles.pillScrollContent}
          showsHorizontalScrollIndicator={false}
        >
          {priceMaxOptions.map((option) => {
            const isSelected = offerNegotiationConfig.priceMax === option.id.toString();
            return (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.filterPill,
                  isSelected && styles.selectedFilterPill
                ]}
                onPress={() => {
                  const newValue = isSelected ? '' : option.id.toString();
                  updateOfferNegotiationConfig('priceMax', newValue);
                }}
              >
                <Text
                  style={[
                    styles.filterPillText,
                    isSelected && styles.selectedFilterPillText
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>

      {/* Save Button */}
      <TouchableOpacity
        style={styles.saveButton}
        onPress={() => onSave(offerNegotiationConfig)}
      >
        <Text style={styles.saveButtonText}>Save</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  configContainer: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  configTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  fullWidthContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  pillScrollContainer: {
    flexDirection: 'row',
  },
  pillScrollContent: {
    paddingRight: 16,
  },
  filterPill: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#B89C4C',
    backgroundColor: '#fff',
    marginRight: 8,
    minHeight: 32,
    justifyContent: 'center',
  },
  selectedFilterPill: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  filterPillText: {
    fontSize: 12,
    color: '#B89C4C',
    fontWeight: '500',
    textAlign: 'center',
  },
  selectedFilterPillText: {
    color: '#fff',
  },
  saveButton: {
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 16,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
});
