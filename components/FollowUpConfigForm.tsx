import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Switch,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Calendar } from 'lucide-react-native';

interface FollowUpConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface FollowUpConfigFormProps {
  followUpConfig: FollowUpConfig;
  updateFollowUpConfig: (field: keyof FollowUpConfig, value: string | boolean) => void;
  handleDatePress: (configType: 'followUp') => void;
  onSave: (config: FollowUpConfig) => void;
}

export default function FollowUpConfigForm({
  followUpConfig,
  updateFollowUpConfig,
  handleDatePress,
  onSave,
}: FollowUpConfigFormProps) {
  const [showDatePicker, setShowDatePicker] = useState(false);

  const handleLocalDatePress = () => {
    setShowDatePicker(true);
  };

  const handleDateChange = (_event: any, selectedDate?: Date) => {
    setShowDatePicker(false);

    if (selectedDate) {
      const formattedDate = selectedDate.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }) + ', ' + selectedDate.toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      });

      updateFollowUpConfig('dueDate', formattedDate);
    }
  };
  return (
    <View style={styles.configContainer}>
      <Text style={styles.configTitle}>Lead configuration</Text>

      {/* Reminder Section */}
      <Text style={styles.sectionTitle}>Reminder</Text>
      
      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Title</Text>
        <TextInput
          style={styles.textInput}
          placeholder="Follow up"
          value={followUpConfig.title}
          onChangeText={(text) => updateFollowUpConfig('title', text)}
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Content</Text>
        <TextInput
          style={[styles.textInput, styles.textArea]}
          placeholder="Follow up reminder."
          value={followUpConfig.content}
          onChangeText={(text) => updateFollowUpConfig('content', text)}
          multiline
          numberOfLines={3}
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Due Date</Text>
        <View style={styles.dateInputContainer}>
          <TouchableOpacity
            style={styles.dateButton}
            onPress={handleLocalDatePress}
          >
            <Text style={styles.dateButtonText}>
              {followUpConfig.dueDate || 'Select date and time'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.calendarButton}
            onPress={handleLocalDatePress}
          >
            <Calendar size={20} color="#6B7280" />
          </TouchableOpacity>
        </View>
        {/* Inline Date Picker */}
        {showDatePicker && (
          <View style={styles.inlineDatePicker}>
            <DateTimePicker
              value={new Date()}
              mode="date"
              display="default"
              onChange={handleDateChange}
            />
          </View>
        )}
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Priority</Text>
        <View style={styles.priorityContainer}>
          {['Low', 'Medium', 'High'].map((priority) => (
            <TouchableOpacity
              key={priority}
              style={[
                styles.priorityButton,
                followUpConfig.priority === priority && styles.priorityButtonActive
              ]}
              onPress={() => updateFollowUpConfig('priority', priority)}
            >
              <Text
                style={[
                  styles.priorityButtonText,
                  followUpConfig.priority === priority && styles.priorityButtonTextActive
                ]}
              >
                {priority}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.switchContainer}>
        <Text style={styles.switchLabel}>Send Email</Text>
        <Switch
          value={followUpConfig.sendEmail}
          onValueChange={(value) => updateFollowUpConfig('sendEmail', value)}
          trackColor={{ false: '#E5E7EB', true: '#B89C4C' }}
          thumbColor={followUpConfig.sendEmail ? '#fff' : '#fff'}
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Remarks</Text>
        <TextInput
          style={[styles.textInput, styles.textArea]}
          placeholder="Additional remarks..."
          value={followUpConfig.remarks}
          onChangeText={(text) => updateFollowUpConfig('remarks', text)}
          multiline
          numberOfLines={3}
        />
      </View>

      {/* Save Button */}
      <TouchableOpacity 
        style={styles.saveButton} 
        onPress={() => onSave(followUpConfig)}
      >
        <Text style={styles.saveButtonText}>Save</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  configContainer: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  configTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#111827',
    backgroundColor: '#fff',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: '#fff',
  },
  dateButtonText: {
    fontSize: 14,
    color: '#6B7280',
  },
  calendarButton: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderLeftWidth: 0,
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  inlineDatePicker: {
    marginTop: 8,
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
  },
  priorityContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  priorityButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#fff',
  },
  priorityButtonActive: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  priorityButtonText: {
    fontSize: 14,
    color: '#6B7280',
  },
  priorityButtonTextActive: {
    color: '#fff',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  switchLabel: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },
  saveButton: {
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 16,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
});
